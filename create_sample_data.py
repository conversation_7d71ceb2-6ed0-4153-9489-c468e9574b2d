#!/usr/bin/env python
"""
Script to create sample hierarchical download data
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mcshop.settings')
django.setup()

from shop.models import Platform, Launcher, Version

def create_sample_data():
    print("Creating sample hierarchical download data...")
    
    # Create Windows platform
    windows, created = Platform.objects.get_or_create(
        name="windows",
        defaults={
            'display_name': "Windows",
            'help': "Download for Windows operating system",
            'enabled': True,
            'published': True
        }
    )
    print(f"Windows platform: {'created' if created else 'already exists'}")

    # Create Android platform
    android, created = Platform.objects.get_or_create(
        name="android",
        defaults={
            'display_name': "Android",
            'help': "Download for Android devices",
            'enabled': True,
            'published': True
        }
    )
    print(f"Android platform: {'created' if created else 'already exists'}")

    # Create TLauncher for Windows
    tlauncher, created = Launcher.objects.get_or_create(
        name="tlauncher",
        platform=windows,
        defaults={
            'display_name': "TLauncher",
            'help': "Popular Minecraft launcher for Windows",
            'enabled': True,
            'published': True
        }
    )
    print(f"TLauncher: {'created' if created else 'already exists'}")

    # Create xLauncher for Windows
    xlauncher, created = Launcher.objects.get_or_create(
        name="xlauncher",
        platform=windows,
        defaults={
            'display_name': "xLauncher",
            'help': "Alternative Minecraft launcher for Windows",
            'enabled': True,
            'published': True
        }
    )
    print(f"xLauncher: {'created' if created else 'already exists'}")

    # Create PojavLauncher for Android
    pojav, created = Launcher.objects.get_or_create(
        name="pojavlauncher",
        platform=android,
        defaults={
            'display_name': "PojavLauncher",
            'help': "Minecraft launcher for Android devices",
            'enabled': True,
            'published': True
        }
    )
    print(f"PojavLauncher: {'created' if created else 'already exists'}")

    # Create versions for TLauncher
    tlauncher_1214, created = Version.objects.get_or_create(
        name="1.21.4",
        launcher=tlauncher,
        defaults={
            'display_name': "Minecraft 1.21.4",
            'help': "Latest stable version",
            'download_url': "https://example.com/tlauncher-1.21.4.exe",
            'enabled': True,
            'published': True
        }
    )
    print(f"TLauncher 1.21.4: {'created' if created else 'already exists'}")

    tlauncher_1215, created = Version.objects.get_or_create(
        name="1.21.5",
        launcher=tlauncher,
        defaults={
            'display_name': "Minecraft 1.21.5",
            'help': "Beta version",
            'download_url': "https://example.com/tlauncher-1.21.5.exe",
            'enabled': True,
            'published': True
        }
    )
    print(f"TLauncher 1.21.5: {'created' if created else 'already exists'}")

    # Create versions for xLauncher
    xlauncher_1216, created = Version.objects.get_or_create(
        name="1.21.6",
        launcher=xlauncher,
        defaults={
            'display_name': "Minecraft 1.21.6",
            'help': "Latest experimental version",
            'download_url': "https://example.com/xlauncher-1.21.6.exe",
            'enabled': True,
            'published': True
        }
    )
    print(f"xLauncher 1.21.6: {'created' if created else 'already exists'}")

    xlauncher_1215, created = Version.objects.get_or_create(
        name="1.21.5",
        launcher=xlauncher,
        defaults={
            'display_name': "Minecraft 1.21.5",
            'help': "Stable version for xLauncher",
            'download_url': "https://example.com/xlauncher-1.21.5.exe",
            'enabled': True,
            'published': True
        }
    )
    print(f"xLauncher 1.21.5: {'created' if created else 'already exists'}")

    # Create versions for PojavLauncher
    pojav_15, created = Version.objects.get_or_create(
        name="1.5",
        launcher=pojav,
        defaults={
            'display_name': "PojavLauncher 1.5",
            'help': "Stable version for Android",
            'download_url': "https://example.com/pojav-1.5.apk",
            'enabled': True,
            'published': True
        }
    )
    print(f"PojavLauncher 1.5: {'created' if created else 'already exists'}")

    pojav_20, created = Version.objects.get_or_create(
        name="2.0",
        launcher=pojav,
        defaults={
            'display_name': "PojavLauncher 2.0",
            'help': "Latest version for Android",
            'download_url': "https://example.com/pojav-2.0.apk",
            'enabled': True,
            'published': True
        }
    )
    print(f"PojavLauncher 2.0: {'created' if created else 'already exists'}")

    print("\nSample data creation completed!")
    
    # Print summary
    print(f"\nSummary:")
    print(f"Platforms: {Platform.objects.count()}")
    print(f"Launchers: {Launcher.objects.count()}")
    print(f"Versions: {Version.objects.count()}")

if __name__ == "__main__":
    create_sample_data()
