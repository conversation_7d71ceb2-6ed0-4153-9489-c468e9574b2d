from django.contrib import admin
from django.contrib import messages
from django.utils.html import format_html
from django.urls import reverse
from django_q.tasks import async_task
from .models import MinecraftServer, Category, ContentCreator, Item, Purchase, PurchaseItem, CommandJob, PlayerCountSnapshot, Platform, Launcher, Version


@admin.register(MinecraftServer)
class MinecraftServerAdmin(admin.ModelAdmin):
    list_display = ['name', 'display_name', 'ip', 'port', 'domain', 'rcon_port', 'enabled', 'published', 'proxy']
    list_filter = ['enabled', 'published', 'proxy']
    search_fields = ['name', 'display_name', 'ip', 'domain']
    list_editable = ['enabled', 'published']

    # Group fields for better organization
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'display_name', 'enabled', 'published')
        }),
        ('Connection Settings', {
            'fields': ('ip', 'port', 'domain', 'proxy')
        }),
        ('RCON Configuration', {
            'fields': ('rcon_port', 'rcon_password'),
            'description': 'RCON settings for remote command execution'
        }),
        ('Other Ports', {
            'fields': ('query_port',),
            'classes': ('collapse',)
        }),
    )


@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'display_name', 'enabled', 'published']
    list_filter = ['enabled', 'published']
    search_fields = ['name', 'display_name']
    list_editable = ['enabled', 'published']


@admin.register(ContentCreator)
class ContentCreatorAdmin(admin.ModelAdmin):
    list_display = ['name', 'display_name', 'enabled', 'published']
    list_filter = ['enabled', 'published']
    search_fields = ['name', 'display_name']
    list_editable = ['enabled', 'published']


@admin.register(Item)
class ItemAdmin(admin.ModelAdmin):
    list_display = ['name', 'display_name', 'category', 'price', 'ucoin_price','expiration_days', 'minecraft_server', 'enabled', 'published']
    list_filter = ['category','expiration_days', 'minecraft_server', 'enabled', 'published']
    search_fields = ['name', 'display_name']
    list_editable = ['enabled', 'published']


class CommandJobInline(admin.TabularInline):
    model = CommandJob
    extra = 0
    readonly_fields = ['created_at', 'started_at', 'completed_at']
    fields = ['command_text', 'sequence_order', 'state', 'error_message', 'created_at', 'started_at', 'completed_at']
    ordering = ['sequence_order']


class PurchaseItemInline(admin.TabularInline):
    model = PurchaseItem
    extra = 0
    readonly_fields = ['created_at']
    fields = ['item', 'quantity', 'subscription_status', 'expires_at', 'created_at']


@admin.register(Purchase)
class PurchaseAdmin(admin.ModelAdmin):
    list_display = ['ref_id', 'get_items_display', 'minecraft_username', 'state', 'get_derived_state', 'created_at', 'payment_succeeded_at']
    list_filter = ['state', 'created_at', 'payment_succeeded_at']
    search_fields = ['ref_id', 'minecraft_username', 'mobile_number', 'zarinpal_ref_id']
    readonly_fields = ['ref_id', 'created_at', 'payment_succeeded_at', 'authority', 'zarinpal_ref_id', 'zarinpal_code', 'zarinpal_verify_response']
    date_hierarchy = 'created_at'
    inlines = [PurchaseItemInline]
    actions = ['retry_failed_commands']

    def get_items_display(self, obj):
        items = obj.purchase_items.all()
        if items.count() == 1:
            item = items.first()
            return f"{item.item.name} x{item.quantity}"
        else:
            return f"{items.count()} items"
    get_items_display.short_description = 'Items'

    def get_derived_state(self, obj):
        return obj.get_derived_state_from_commands()
    get_derived_state.short_description = 'Derived State'

    def retry_failed_commands(self, request, queryset):
        """Admin action to retry failed commands for selected purchases"""
        retried_count = 0
        skipped_count = 0

        for purchase in queryset:
            # Check if purchase has failed commands
            failed_jobs = CommandJob.objects.filter(
                purchase_item__purchase=purchase,
                state=CommandJob.State.FAILED
            )

            if failed_jobs.exists():
                # Reset failed jobs to pending
                failed_jobs.update(
                    state=CommandJob.State.PENDING,
                    error_message=None,
                    started_at=None,
                    completed_at=None
                )

                # Queue purchase for re-execution
                async_task('shop.tasks.execute_purchase_commands', purchase.id)
                retried_count += 1
            else:
                skipped_count += 1

        if retried_count > 0:
            self.message_user(
                request,
                f'Successfully queued {retried_count} purchase(s) for command retry.',
                messages.SUCCESS
            )

        if skipped_count > 0:
            self.message_user(
                request,
                f'Skipped {skipped_count} purchase(s) with no failed commands.',
                messages.WARNING
            )

    retry_failed_commands.short_description = "Retry failed commands for selected purchases"


@admin.register(PurchaseItem)
class PurchaseItemAdmin(admin.ModelAdmin):
    list_display = ['get_purchase_link', 'item', 'quantity', 'subscription_status', 'get_command_state', 'expires_at', 'created_at']
    list_filter = ['subscription_status', 'created_at', 'item']
    search_fields = ['purchase__ref_id', 'purchase__minecraft_username', 'item__name']
    readonly_fields = ['created_at']
    date_hierarchy = 'created_at'
    inlines = [CommandJobInline]
    actions = ['retry_failed_commands']

    def get_purchase_link(self, obj):
        """Create a clickable link to the purchase admin page"""
        url = reverse('admin:shop_purchase_change', args=[obj.purchase.id])
        return format_html('<a href="{}">{}</a>', url, obj.purchase)
    get_purchase_link.short_description = 'Purchase'

    def get_command_state(self, obj):
        return obj.get_command_execution_state()
    get_command_state.short_description = 'Command State'

    def retry_failed_commands(self, request, queryset):
        """Admin action to retry failed commands for selected purchase items"""
        retried_count = 0
        skipped_count = 0

        for purchase_item in queryset:
            # Check if purchase item has failed commands
            failed_jobs = purchase_item.command_jobs.filter(state=CommandJob.State.FAILED)

            if failed_jobs.exists():
                # Reset failed jobs to pending
                failed_jobs.update(
                    state=CommandJob.State.PENDING,
                    error_message=None,
                    started_at=None,
                    completed_at=None
                )

                # Queue purchase item for re-execution
                async_task('shop.tasks.execute_purchase_item_commands', purchase_item.id)
                retried_count += 1
            else:
                skipped_count += 1

        if retried_count > 0:
            self.message_user(
                request,
                f'Successfully queued {retried_count} purchase item(s) for command retry.',
                messages.SUCCESS
            )

        if skipped_count > 0:
            self.message_user(
                request,
                f'Skipped {skipped_count} purchase item(s) with no failed commands.',
                messages.WARNING
            )

    retry_failed_commands.short_description = "Retry failed commands for selected purchase items"


@admin.register(CommandJob)
class CommandJobAdmin(admin.ModelAdmin):
    list_display = ['id', 'get_purchase_info', 'get_item_name', 'get_command_preview', 'state', 'sequence_order', 'created_at', 'completed_at']
    list_filter = ['state', 'created_at', 'purchase_item__item']
    search_fields = ['purchase_item__purchase__ref_id', 'purchase_item__purchase__minecraft_username', 'command_text']
    readonly_fields = ['created_at', 'started_at', 'completed_at', 'django_q_task_id']
    date_hierarchy = 'created_at'
    ordering = ['-created_at']
    actions = ['retry_failed_commands']

    def get_purchase_info(self, obj):
        """Create a clickable link to the purchase admin page"""
        url = reverse('admin:shop_purchase_change', args=[obj.purchase_item.purchase.id])
        return format_html('<a href="{}">{}</a>', url, obj.purchase_item.purchase)
    get_purchase_info.short_description = 'Purchase'

    def get_item_name(self, obj):
        return obj.purchase_item.item.name
    get_item_name.short_description = 'Item'

    def get_command_preview(self, obj):
        return obj.command_text[:50] + ('...' if len(obj.command_text) > 50 else '')
    get_command_preview.short_description = 'Command'

    def retry_failed_commands(self, request, queryset):
        """Admin action to retry selected failed command jobs"""
        # Calculate counts before making any changes
        failed_jobs = queryset.filter(state=CommandJob.State.FAILED)
        failed_jobs_count = failed_jobs.count()
        non_failed_jobs_count = queryset.exclude(state=CommandJob.State.FAILED).count()

        if failed_jobs_count > 0:
            # Get job IDs before updating
            failed_job_ids = list(failed_jobs.values_list('id', flat=True))

            # Reset failed jobs to pending
            failed_jobs.update(
                state=CommandJob.State.PENDING,
                error_message=None,
                started_at=None,
                completed_at=None
            )

            # Queue each job for individual execution
            for job_id in failed_job_ids:
                async_task('shop.tasks.execute_single_command', job_id)

            self.message_user(
                request,
                f'Successfully queued {failed_jobs_count} failed command job(s) for retry.',
                messages.SUCCESS
            )

        if non_failed_jobs_count > 0:
            self.message_user(
                request,
                f'Skipped {non_failed_jobs_count} command job(s) that are not in failed state.',
                messages.WARNING
            )

    retry_failed_commands.short_description = "Retry selected failed command jobs"


class LauncherInline(admin.TabularInline):
    model = Launcher
    extra = 0
    fields = ['name', 'display_name', 'download_url', 'image_url', 'enabled', 'published']
    show_change_link = True


class VersionInline(admin.TabularInline):
    model = Version
    extra = 0
    fields = ['name', 'display_name', 'download_url', 'image_url', 'enabled', 'published']


@admin.register(Platform)
class PlatformAdmin(admin.ModelAdmin):
    list_display = ['name', 'display_name', 'get_launchers_count', 'enabled', 'published', 'created_at']
    list_filter = ['enabled', 'published', 'created_at']
    search_fields = ['name', 'display_name']
    list_editable = ['enabled', 'published']
    inlines = [LauncherInline]

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'display_name', 'enabled', 'published')
        }),
        ('Media & Download', {
            'fields': ('image_url', 'download_url', 'help'),
            'description': 'Help field supports Markdown formatting'
        }),
    )

    def get_launchers_count(self, obj):
        return obj.launchers.count()
    get_launchers_count.short_description = 'Launchers'


@admin.register(Launcher)
class LauncherAdmin(admin.ModelAdmin):
    list_display = ['name', 'display_name', 'platform', 'get_versions_count', 'enabled', 'published', 'created_at']
    list_filter = ['platform', 'enabled', 'published', 'created_at']
    search_fields = ['name', 'display_name', 'platform__name']
    list_editable = ['enabled', 'published']
    inlines = [VersionInline]

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'display_name', 'platform', 'enabled', 'published')
        }),
        ('Media & Download', {
            'fields': ('image_url', 'download_url', 'help'),
            'description': 'Help field supports Markdown formatting'
        }),
    )

    def get_versions_count(self, obj):
        return obj.versions.count()
    get_versions_count.short_description = 'Versions'


@admin.register(Version)
class VersionAdmin(admin.ModelAdmin):
    list_display = ['name', 'display_name', 'get_launcher_info', 'enabled', 'published', 'created_at']
    list_filter = ['launcher__platform', 'launcher', 'enabled', 'published', 'created_at']
    search_fields = ['name', 'display_name', 'launcher__name', 'launcher__platform__name']
    list_editable = ['enabled', 'published']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'display_name', 'launcher', 'enabled', 'published')
        }),
        ('Media & Download', {
            'fields': ('image_url', 'download_url', 'help'),
            'description': 'Help field supports Markdown formatting'
        }),
    )

    def get_launcher_info(self, obj):
        return f"{obj.launcher.platform.name} - {obj.launcher.name}"
    get_launcher_info.short_description = 'Platform - Launcher'


@admin.register(PlayerCountSnapshot)
class PlayerCountSnapshotAdmin(admin.ModelAdmin):
    list_display = ['server', 'timestamp', 'online_players', 'max_players', 'query_successful']
    list_filter = ['server', 'query_successful', 'timestamp']
    search_fields = ['server__name', 'server__display_name']
    readonly_fields = ['timestamp', 'server', 'online_players', 'max_players', 'query_successful', 'error_message']
    date_hierarchy = 'timestamp'
    ordering = ['-timestamp']

    def has_add_permission(self, request):
        # Prevent manual creation of snapshots through admin
        return False

    def has_change_permission(self, request, obj=None):
        # Make snapshots read-only
        return False