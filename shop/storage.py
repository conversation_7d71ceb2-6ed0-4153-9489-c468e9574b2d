import os
import uuid

import boto3
from botocore.exceptions import ClientError
from django.conf import settings
from django.core.files.storage import Storage
from django.utils.deconstruct import deconstructible


def _get_content_type(filename):
    """Get content type based on file extension."""
    ext = os.path.splitext(filename)[1].lower()
    content_types = {
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.png': 'image/png',
        '.gif': 'image/gif',
        '.webp': 'image/webp',
    }
    return content_types.get(ext, 'application/octet-stream')


@deconstructible
class ArvanS3Storage(Storage):
    """
    Custom storage backend for Arvan Cloud S3-compatible storage.
    """

    def __init__(self, folder_name='NA'):
        self.bucket_name = settings.ARVAN_S3_BUCKET_NAME
        self.folder_name = folder_name
        self.endpoint_url = settings.ARVAN_S3_ENDPOINT_URL
        self.access_key = settings.ARVAN_S3_ACCESS_KEY_ID
        self.secret_key = settings.ARVAN_S3_SECRET_ACCESS_KEY
        
        # Initialize S3 client
        self.s3_client = boto3.client(
            's3',
            endpoint_url=self.endpoint_url,
            aws_access_key_id=self.access_key,
            aws_secret_access_key=self.secret_key,
            region_name='ir-thr-at1'  # Arvan Cloud region
        )
    
    def _get_s3_key(self, name):
        """Generate S3 key with folder prefix."""
        return f"{self.folder_name}/{name}"
    
    def _generate_filename(self, filename):
        """Generate a unique filename to avoid conflicts."""
        ext = os.path.splitext(filename)[1]
        unique_filename = f"{os.path.splitext(filename)[0]}-{uuid.uuid4().hex}{ext}"
        return unique_filename
    
    def _save(self, name, content):
        """Save file to S3."""
        # Generate unique filename
        filename = self._generate_filename(name)
        s3_key = self._get_s3_key(filename)
        
        try:
            # Upload file to S3
            self.s3_client.upload_fileobj(
                content,
                self.bucket_name,
                s3_key,
                ExtraArgs={
                    'ACL': 'public-read',  # Make file publicly accessible
                    'ContentType': _get_content_type(filename)
                }
            )
            return filename
        except ClientError as e:
            raise IOError(f"Failed to upload file to S3: {e}")

    def delete(self, name):
        """Delete file from S3."""
        s3_key = self._get_s3_key(name)
        try:
            self.s3_client.delete_object(Bucket=self.bucket_name, Key=s3_key)
        except ClientError as e:
            # Don't raise error if file doesn't exist
            if e.response['Error']['Code'] != 'NoSuchKey':
                raise IOError(f"Failed to delete file from S3: {e}")
    
    def exists(self, name):
        """Check if file exists in S3."""
        s3_key = self._get_s3_key(name)
        try:
            self.s3_client.head_object(Bucket=self.bucket_name, Key=s3_key)
            return True
        except ClientError:
            return False
    
    def url(self, name):
        """Generate public URL for the file."""
        if not name:
            return None
        s3_key = self._get_s3_key(name)
        # Generate public URL
        return f"{self.endpoint_url}/{self.bucket_name}/{s3_key}"
    
    def size(self, name):
        """Get file size."""
        s3_key = self._get_s3_key(name)
        try:
            response = self.s3_client.head_object(Bucket=self.bucket_name, Key=s3_key)
            return response['ContentLength']
        except ClientError:
            raise IOError(f"File {name} not found in S3")
    
    def get_accessed_time(self, name):
        """Not supported by S3."""
        raise NotImplementedError("S3 storage doesn't support accessed time")
    
    def get_created_time(self, name):
        """Get file creation time."""
        s3_key = self._get_s3_key(name)
        try:
            response = self.s3_client.head_object(Bucket=self.bucket_name, Key=s3_key)
            return response['LastModified']
        except ClientError:
            raise IOError(f"File {name} not found in S3")
    
    def get_modified_time(self, name):
        """Get file modification time (same as creation time for S3)."""
        return self.get_created_time(name)
    
    def listdir(self, path):
        """List directory contents."""
        if path:
            prefix = f"{self.folder_name}/{path}/"
        else:
            prefix = f"{self.folder_name}/"
        
        try:
            response = self.s3_client.list_objects_v2(
                Bucket=self.bucket_name,
                Prefix=prefix,
                Delimiter='/'
            )
            
            directories = []
            files = []
            
            # Get subdirectories
            for obj in response.get('CommonPrefixes', []):
                dir_name = obj['Prefix'][len(prefix):].rstrip('/')
                directories.append(dir_name)
            
            # Get files
            for obj in response.get('Contents', []):
                if obj['Key'] != prefix:  # Exclude the directory itself
                    file_name = obj['Key'][len(prefix):]
                    if '/' not in file_name:  # Only direct files, not subdirectory files
                        files.append(file_name)
            
            return directories, files
        except ClientError as e:
            raise IOError(f"Failed to list directory: {e}")


# Specific storage instances for different model types
class ItemImageStorage(ArvanS3Storage):
    """Storage for Item images in skus folder."""
    def __init__(self):
        super().__init__(folder_name='skus')


class ContentCreatorImageStorage(ArvanS3Storage):
    """Storage for ContentCreator images in creators folder."""
    def __init__(self):
        super().__init__(folder_name='ccs')


class PlatformImageStorage(ArvanS3Storage):
    """Storage for Platform images in launchers/{platformName} folder."""
    def __init__(self, instance=None):
        if instance and hasattr(instance, 'name'):
            folder_name = f'launchers/{instance.name}'
        else:
            folder_name = 'launchers/platforms'  # fallback
        super().__init__(folder_name=folder_name)


class LauncherImageStorage(ArvanS3Storage):
    """Storage for Launcher images in launchers/{platformName}/{launcherName} folder."""
    def __init__(self, instance=None):
        if instance and hasattr(instance, 'platform') and hasattr(instance, 'name'):
            folder_name = f'launchers/{instance.platform.name}/{instance.name}'
        else:
            folder_name = 'launchers/launchers'  # fallback
        super().__init__(folder_name=folder_name)


class VersionImageStorage(ArvanS3Storage):
    """Storage for Version images in launchers/{platformName}/{launcherName}/versions folder."""
    def __init__(self, instance=None):
        if (instance and hasattr(instance, 'launcher') and
            hasattr(instance.launcher, 'platform') and
            hasattr(instance.launcher, 'name')):
            folder_name = f'launchers/{instance.launcher.platform.name}/{instance.launcher.name}/versions'
        else:
            folder_name = 'launchers/versions'  # fallback
        super().__init__(folder_name=folder_name)
