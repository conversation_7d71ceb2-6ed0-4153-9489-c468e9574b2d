from rest_framework import serializers
from .models import Purchase, PurchaseItem, MinecraftServer, Category, ContentCreator, Item, Platform, Launcher, Version


class MinecraftServerSerializer(serializers.ModelSerializer):
    class Meta:
        model = MinecraftServer
        fields = ['id', 'name', 'display_name', 'domain', 'enabled']


class CategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = Category
        fields = ['id', 'name', 'display_name', 'description', 'enabled']


class ContentCreatorSerializer(serializers.ModelSerializer):
    class Meta:
        model = ContentCreator
        fields = ['id', 'name', 'display_name', 'description', 'image', 'enabled']


class ItemSerializer(serializers.ModelSerializer):
    class Meta:
        model = Item
        fields = ['id', 'name', 'display_name', 'description', 'image', 'category', 'price',
                  'ucoin_price', 'expiration_days', 'is_one_time', 'description', 'enabled']


class PurchaseItemSerializer(serializers.ModelSerializer):
    class Meta:
        model = PurchaseItem
        fields = ['item', 'quantity']


class PurchaseSerializer(serializers.ModelSerializer):
    items = PurchaseItemSerializer(source='purchase_items', many=True, write_only=True)

    class Meta:
        model = Purchase
        fields = ['id', 'minecraft_username', 'mobile_number', 'items', 'referrer']

    def create(self, validated_data):
        items_data = validated_data.pop('purchase_items')
        purchase = Purchase.objects.create(**validated_data)

        for item_data in items_data:
            PurchaseItem.objects.create(purchase=purchase, **item_data)

        return purchase


class VersionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Version
        fields = ['id', 'name', 'display_name', 'help', 'download_url', 'image_url', 'enabled']


class LauncherSerializer(serializers.ModelSerializer):
    versions = VersionSerializer(many=True, read_only=True)

    class Meta:
        model = Launcher
        fields = ['id', 'name', 'display_name', 'help', 'download_url', 'image_url', 'enabled', 'versions']


class PlatformSerializer(serializers.ModelSerializer):
    launchers = LauncherSerializer(many=True, read_only=True)

    class Meta:
        model = Platform
        fields = ['id', 'name', 'display_name', 'help', 'download_url', 'image_url', 'enabled', 'launchers']


class HierarchicalDownloadLinksSerializer(serializers.Serializer):
    """
    Serializer for the complete hierarchical download links structure.
    Returns only published items in the hierarchy (enabled status is included for frontend).
    """
    platforms = PlatformSerializer(many=True, read_only=True)

    def to_representation(self, instance):
        # Filter to only include published items (enabled status is preserved for frontend)
        platforms = Platform.objects.filter(
            published=True
        ).prefetch_related(
            'launchers__versions'
        )

        # Further filter launchers and versions
        filtered_platforms = []
        for platform in platforms:
            # Get published launchers for this platform
            launchers = platform.launchers.filter(published=True)

            filtered_launchers = []
            for launcher in launchers:
                # Get published versions for this launcher
                versions = launcher.versions.filter(published=True)

                # Only include launcher if it has versions or a direct download URL
                if versions.exists() or launcher.download_url:
                    launcher_data = LauncherSerializer(launcher).data
                    launcher_data['versions'] = VersionSerializer(versions, many=True).data
                    filtered_launchers.append(launcher_data)

            # Only include platform if it has launchers or a direct download URL
            if filtered_launchers or platform.download_url:
                platform_data = PlatformSerializer(platform).data
                platform_data['launchers'] = filtered_launchers
                filtered_platforms.append(platform_data)

        return {'platforms': filtered_platforms}
